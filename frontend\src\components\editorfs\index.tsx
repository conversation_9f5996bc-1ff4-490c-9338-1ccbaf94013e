import examManageApis from '@/api/exam';
import { getSensitiveWord, textInit } from '@/utils';
import { PaperClipOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import './index.less';
// import pluginmin from '@wiris/mathtype-tinymce5';
import tupIcon from '@/images/icons/tup.png';
import muisIcon from '@/images/icons/muis.png';
interface editor {
  name?: any;
  value?: any;
  textSetting?: any;
  height?: any;
  /** 禁用图片 */
  disabledImage?: boolean;
  onChange?: (e: any) => void;
  addBlanks?: () => void;
  addFile?: () => void;
  addCase?: () => void;
  addtupFile?: () => void;
  // onChange?:any => ()
}
const DEFAULT_TYPES = 'jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp';
const IMAGE_TYPES = `${DEFAULT_TYPES},${DEFAULT_TYPES.toUpperCase()}`;

const Editorfs: React.FC<editor> = (props) => {
  const {
    name,
    value,
    height,
    textSetting,
    disabledImage,
    onChange,
    addBlanks,
    addFile,
    addtupFile,
    addCase
  } = props;
  const [isChange, setIsChange] = useState<boolean>(false); // 表单值是否有变化
  const [initInstance, setInitInstance] = useState<boolean>(false); // 编辑器是否初始化完毕
  const [flage, setFlage] = useState<boolean>(true); // 防止重复设置  光标乱跑问题

  useEffect(() => {
    // console.log('value',value);
    if (value && initInstance) {
      if (name && value !== '<p><br data-mce-bogus="1"></p>' && flage) {
        // 手动规避editor获取焦点必会触发的bug
        (window as any).tinymce.editors[name]?.setContent(value);
        setFlage(false);
      }
    }
  }, [value, initInstance]);
  useEffect(() => {
    (window as any).tinymce.editors[name]?.remove();
    initEditor();
  }, []);
  // 初始化富文本编辑器
  const initEditor = () => {
    (window as any).tinymce
      .init({
        selector: `#${name}`,
        // toolbar: 'undo redo | styleselect | bold italic | link image strikethrough tiny_mce_wiris_formulaEditor' mathjax
        // toolbar: `bold italic underline ${
        //   disabledImage ? '' : 'image'
        // } kityformula-editor tiny_mce_wiris_formulaEditorChemistry ${
        //   addBlanks ? 'addblanks' : ''
        // } ${addFile ? 'addFile' : ''} sobeymath ${addCase ? 'addCase' : ''}`,  
        toolbar: `${addtupFile ? 'addtupFile' : ''} ${addFile ? 'addFile' : ''}`, // 只保留图片和附件
        menubar: '',
        // height: height,
        // automatic_uploads: false, //图像是使用imagetools插件处理后插入到内容区的，此时图像并未真正上传到服务器，而是以Data URL/Blob URL的方式插入在内容中
        language: 'zh_CN',
        // external_plugins: { tiny_mce_wiris: 'https://www.wiris.net/demo/plugins/tiny_mce/plugin.js' }, //注册公式插件 media
        external_plugins: {
          // tiny_mce_wiris: `/exam/wiris/mathtype-tinymce5/plugin.min.js`,
          // mathjax: `${location.pathname}tinymce/plugins/mathjax/plugin.js`,
        },
        plugins: 'image kityformula-editor wordcount sobeymath',
        // mathjax  字体下载 https://www.cdnpkg.com/mathjax?id=53958
        // mathjax: {
        //   lib: `${location.pathname}tinymce/tex-mml-chtml.js`,
        // },
        valid_elements: '*[*]',
        // images_upload_base_path: '/demo',
        // images_upload_handler: images_upload_handler, //图片上传
        // images_file_types: IMAGE_TYPES,
        // image_caption: true,
        // mathTypeParameters : {
        //     serviceProviderProperties : {
        //         URI : '/pluginwiris_engine/app/configurationjs',
        //         server : 'java'
        //     }
        // },
        powerpaste_word_import: 'propmt', // 参数可以是propmt, merge, clear，效果自行切换对比
        powerpaste_html_import: 'propmt', // propmt, merge, clear
        powerpaste_allow_local_images: true,
        paste_data_images: true,
        powerpaste_keep_unsupported_src: true,
        length: 10,
        powerpaste_block_drop: false,
        imagetools_cors_hosts: ['img-blog.csdnimg.cn', 'statics.xiumi.us'], //这个是第三方插件 需要配置 plugins 里面的 imagetools  然后加上图片的域名 防止跨域
        setup: function (editor: any) {
          editor.on('change', function (e: any) {
            // console.log(e);
            onChange && onChange(e);
          });
          editor.on('paste input', function (e: any) {
            textInit(e, editor, textSetting);
          });
          console.log(editor.ui.registry.getAll());
          editor.ui.registry.addIcon('attacment', <PaperClipOutlined />);
          editor.ui.registry.addButton('addblanks', {
            text: '<i class="addBlanks_btn">[填空]</i>',
            tooltip: '插入填空',
            // icon:'accessibility-check',
            onAction: function () {
              addBlanks?.();
            },
          });
          editor.ui.registry.addButton('addFile', {
            tooltip: '添加音频',
            // icon: 'link',
            text: `<img src="${muisIcon}"  width="12" height="12" />`,
            onAction: function (e: any) {
              e.preventDefault?.(); // 防止默认行为
              e.stopPropagation?.(); // 阻止事件冒泡
              addFile?.();
            },
          });
          editor.ui.registry.addButton('addtupFile', {
            tooltip: '添加图片',
            // tup.png muis.png
            text: `<img src="${tupIcon}" width="12" height="12" />`,
            onAction: function (e: any) {
              e.preventDefault?.(); // 防止默认行为
              e.stopPropagation?.(); // 阻止事件冒泡
              addtupFile?.();
            },
          });

          if (addCase) {
            editor.ui.registry.addButton('addCase', {
              tooltip: '添加案例',
              text: `<img src="/exam/static/images/case-icon.svg" />`,
              onAction: function () {
                addCase?.();
              },
            });
          }
        },
        init_instance_callback: function (editor: any) {
          setInitInstance(true);
          // console.log('editor',editor);
          if (value) {
            (window as any).tinymce.editors[name].insertContent(value)
          }
        },
      })
      .then(() => { });
  };
  //插入内容 insertContent
  // 单图上传图片
  // const images_upload_handler = (blobInfo: any, succFun: any, failFun: any) => {
  //   const formData = new FormData();
  //   formData.append('file', blobInfo.blob(), blobInfo.filename());
  //   getSensitiveWord(blobInfo.filename(), '文件名', () =>
  //     examManageApis.uploadfile(formData),
  //   ).then((res: any) => {
  //     if (!res) return failFun('取消上传');
  //     if (res.success) {
  //       const domain = window.location.origin; //必须拼上全部地址 否则从富文本拿出来的地址是相对路径 在不同项目中引用会访问不到
  //       succFun(domain + res.data);
  //     } else {
  //       failFun(res.error?.title || '上传出错了');
  //     }
  //   });
  // };

  return <div id={name}></div>;
};

export default Editorfs;
